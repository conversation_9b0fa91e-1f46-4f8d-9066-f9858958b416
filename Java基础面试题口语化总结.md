# 💎 Java基础面试题口语化总结

## 🚀 如何避免空指针异常（NPE）

**问：怎么优雅地避免空指针异常？**

答：空指针异常是Java程序最常见的运行时异常，我们有几种方式来避免：

1. **传统if判断**：虽然能解决问题，但代码嵌套太深，不美观
2. **工具类判断**：用ObjectUtils、StringUtils、CollectionUtils等工具类，代码更简洁
3. **Optional类**：Java8引入的，可以链式调用，配合orElse使用很优雅
4. **断言Assert**：在方法入口处校验参数，提前发现问题
5. **@Nullable注解**：在编译期就能提示可能的空指针风险

**记忆要点**：Optional最优雅，工具类最实用，断言最安全

---

## 🔄 CopyOnWriteArrayList是什么

**问：什么是CopyOnWriteArrayList？**

答：这是一个线程安全的List实现，核心思想是"写时复制"。

**工作原理**：
- 读操作：直接读原数组，不加锁，性能很好
- 写操作：先复制一份新数组，在新数组上修改，最后把引用指向新数组

**优点**：
- 线程安全
- 读操作不加锁，性能好
- 不会抛ConcurrentModificationException

**缺点**：
- 写操作性能差（要复制数组）
- 内存占用大
- 数据一致性问题（可能读到旧数据）

**使用场景**：读多写少、集合不大、实时性要求不高的场景

**记忆口诀**：写时复制保安全，读多写少最适合

---

## 📋 List操作常见问题

**问：Arrays.asList有什么坑？**

答：主要有三个坑：

1. **基本类型数组转换问题**
   - `int[] arr = {1,2,3}; Arrays.asList(arr)` 会把整个数组当成一个元素
   - 解决：用包装类型 `Integer[] arr = {1,2,3}`

2. **返回的List不支持增删**
   - Arrays.asList返回的是内部类ArrayList，不是java.util.ArrayList
   - 解决：`new ArrayList<>(Arrays.asList(arr))`

3. **修改原数组会影响List**
   - 因为返回的List直接引用原数组
   - 解决：同样是new一个新的ArrayList

**问：subList有什么问题？**

答：
1. **不能强转ArrayList**：subList返回的是内部类SubList
2. **修改会相互影响**：subList和原List共享数据
3. **可能导致OOM**：subList持有原List引用，原List无法被回收

**记忆要点**：Arrays.asList和subList都要new一个新的ArrayList来解决问题

---

## 🗺️ HashMap删除元素的方法

**问：如何优雅地删除HashMap元素？**

答：有5种方式，推荐后两种：

1. **增强for循环**：需要用CopyOnWriteArraySet包装避免并发修改异常
2. **forEach循环**：需要用ConcurrentHashMap包装
3. **Iterator迭代器**：调用iterator.remove()，比较安全
4. **removeIf方法**（推荐）：`map.entrySet().removeIf(entry -> condition)`
5. **Stream过滤**（推荐）：`map.entrySet().stream().filter().collect()`

**记忆要点**：removeIf最简洁，Stream最函数式，Iterator最传统但安全

---

## 💰 BigDecimal使用注意事项

**问：使用BigDecimal要注意什么？**

答：主要有三个坑：

1. **构造函数传浮点数有精度问题**
   - 错误：`new BigDecimal(0.01)` 会有精度丢失
   - 正确：`BigDecimal.valueOf(0.01)` 或 `new BigDecimal("0.01")`

2. **equals比较的不是数值大小**
   - `new BigDecimal("0.01").equals(new BigDecimal("0.010"))` 返回false
   - 因为equals还比较精度（scale）
   - 正确做法：用compareTo比较大小

3. **除法要指定精度和舍入模式**
   - 直接除法可能抛ArithmeticException
   - 要指定：`divide(divisor, 2, RoundingMode.HALF_UP)`

**记忆口诀**：valueOf构造，compareTo比较，divide指定精度

---

## 🏗️ CAP定理

**问：什么是CAP定理？**

答：CAP定理说分布式系统不能同时满足三个特性：

- **C（Consistency）一致性**：所有节点同时看到相同数据
- **A（Availability）可用性**：系统一直可用，不会宕机
- **P（Partition tolerance）分区容错性**：网络分区时系统继续工作

**为什么不能兼得？**
网络分区时，要么选择一致性（停止服务等待同步），要么选择可用性（允许数据不一致）

**实际应用**：
- **AP系统**：如社交媒体，优先保证可用性，允许短暂数据不一致
- **CP系统**：如银行系统，优先保证数据一致性，可以短暂停服

**记忆要点**：网络分区时，一致性和可用性只能选一个

---

## 🔢 为什么BigDecimal适合数值计算

**问：为什么说BigDecimal适合精确计算？**

答：BigDecimal通过以下方式保证精度：

1. **内部结构**：
   - `intVal`：存储整数部分（BigInteger）
   - `scale`：小数位数
   - `precision`：总精度

2. **计算原理**：
   - 将小数转换为整数计算（放大）
   - 保持scale信息
   - 计算完成后再还原

3. **优势**：
   - 任意精度
   - 避免浮点数精度丢失
   - 提供丰富的舍入模式

**记忆要点**：BigDecimal = 整数计算 + 精度信息，避免了浮点数的精度问题

---

## 📝 总结记忆技巧

1. **空指针**：Optional最优雅，工具类最实用
2. **CopyOnWrite**：写时复制，读多写少
3. **List操作**：Arrays.asList和subList都要new新的
4. **HashMap删除**：removeIf和Stream最推荐
5. **BigDecimal**：valueOf构造，compareTo比较，divide指定精度
6. **CAP定理**：分区时一致性和可用性二选一
7. **精确计算**：BigDecimal通过整数计算保证精度

**背诵口诀**：空指针用Optional，并发用CopyOnWrite，List操作要new新，删除用removeIf，BigDecimal要小心，CAP不能全要，精度靠整数算。
